% 计算梯度
%注意这里的eta_BS_t实际上应该是eta_k*pi_k^H
function [gradient,delta] = cal_gradient_com(theta_BS_t,theta_BS_CU_t,phi_BS_t,phi_BS_CU_t,tn_iter,p,q,lambda,Lt,a,R)

%计算BS-IRS传输线路的路径差,diff_dis_BS_t 为Lt*1的列向量
x_angle_BS_t = sin(theta_BS_t).* cos(phi_BS_t);
y_angle_BS_t = cos(theta_BS_t);
angle_gather_BS_t = [x_angle_BS_t,y_angle_BS_t];
diff_dis_BS_t = angle_gather_BS_t * tn_iter;

%计算BS-CU传输线路的路径差,diff_dis_BS_t 为Lt*1的列向量
x_angle_BS_CU_t = sin(theta_BS_CU_t).* cos(phi_BS_CU_t);
y_angle_BS_CU_t = cos(theta_BS_CU_t);
angle_gather_BS_CU_t = [x_angle_BS_CU_t,y_angle_BS_CU_t];
diff_dis_BS_CU_t = angle_gather_BS_CU_t * tn_iter;

temp1=0;temp2=0;temp3=0;
for i=1:Lt-1
    for j=i+1:Lt
        temp1=temp1+abs(q(i))*abs(q(j))*sin(2*pi/lambda*(diff_dis_BS_CU_t(j)-diff_dis_BS_CU_t(i))+angle(q(i))-angle(q(j)))*(-sin(theta_BS_CU_t(i))*cos(phi_BS_CU_t(i))+sin(theta_BS_CU_t(j))*cos(phi_BS_CU_t(j)));

        temp2=temp2+abs(p(i))*abs(p(j))*sin(2*pi/lambda*(diff_dis_BS_t(j)-diff_dis_BS_t(i))+angle(p(i))-angle(p(j)))*(sin(theta_BS_t(j))*cos(phi_BS_t(j))-sin(theta_BS_t(i))*cos(phi_BS_t(i)));
    end
end

for i=1:Lt
   for j=1:Lt
       temp3=temp3+abs(p(i))*abs(q(j))*sin(2*pi/lambda*(diff_dis_BS_t(i)-diff_dis_BS_CU_t(j))+angle(q(j))-angle(p(i)))*(cos(phi_BS_t(i))*sin(theta_BS_t(i))-cos(phi_BS_CU_t(j))*sin(theta_BS_CU_t(j)));   
   end
end

temp4=0;temp5=0;
kappa_BS_t = 2*pi/lambda * diff_dis_BS_t + angle(a*conj(p));
temp4= sum(abs(a*p).*sin(theta_BS_t) .* cos(phi_BS_t).*sin(kappa_BS_t));

kappa_BS_CU_t = 2*pi/lambda * diff_dis_BS_CU_t + angle(a*conj(q));
temp5= sum(abs(a*q).*sin(theta_BS_CU_t) .* cos(phi_BS_CU_t).*sin(kappa_BS_CU_t));
del_x = -4*pi/lambda * (temp4 + temp5)-R*4*pi/lambda *(temp1+temp2+temp3);   % 对x求导


temp1=0;temp2=0;temp3=0;
for i=1:Lt-1
    for j=i+1:Lt
        temp1=temp1+abs(q(i))*abs(q(j))*sin(2*pi/lambda*(diff_dis_BS_CU_t(j)-diff_dis_BS_CU_t(i))+angle(q(i))-angle(q(j)))*(-cos(theta_BS_CU_t(i))+cos(theta_BS_CU_t(j)));

        temp2=temp2+abs(p(i))*abs(p(j))*sin(2*pi/lambda*(diff_dis_BS_t(j)-diff_dis_BS_t(i))+angle(p(i))-angle(p(j)))*(-cos(theta_BS_t(i))+cos(theta_BS_t(j)));
    end
end

for i=1:Lt
   for j=1:Lt
       temp3=temp3+abs(p(i))*abs(q(j))*sin(2*pi/lambda*(diff_dis_BS_t(i)-diff_dis_BS_CU_t(j))+angle(q(j))-angle(p(i)))*(cos(theta_BS_t(i))-cos(theta_BS_CU_t(j)));   
   end
end

temp4=0;temp5=0;
kappa_BS_t = 2*pi/lambda * diff_dis_BS_t + angle(a*conj(p));
temp4= sum(abs(a*p).*cos(theta_BS_t) .*sin(kappa_BS_t));

kappa_BS_CU_t = 2*pi/lambda * diff_dis_BS_CU_t + angle(a*conj(q));
temp5= sum(abs(a*q).*cos(theta_BS_CU_t).*sin(kappa_BS_CU_t));

del_y = -4*pi/lambda * (temp4 + temp5)-R*4*pi/lambda *(temp1+temp2+temp3);   % 对x求导


gradient = [del_x , del_y]'; %梯度结果

%% 计算delta的值

temp1=0;temp2=0;temp3=0;
for i=1:Lt-1
   for j=i+1:Lt
       temp1=temp1+abs(p(i))*abs(p(j));
        
       temp2=temp2+abs(q(i))*abs(q(j));
   end
end

for i=1:Lt
   for j=1:Lt
       temp3=temp3+abs(p(i))*abs(q(j));   
   end
end

temp4=sum(abs(a*p)+abs(a*q));

delta=64*pi^2/(lambda^2)*(temp1+temp2+temp3)*abs(R)+16*pi^2/(lambda^2)*temp4;
end