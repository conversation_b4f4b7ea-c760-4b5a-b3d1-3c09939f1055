%H_t:表示从BS到IRS的信道矩阵
%A_theta：表示L个感知方向的导向向量
%R_K:表示基站向第k个用户的放射矩阵，大小为 M*M*K
%v：表示IRS相移的N*1列向量
function [v,chi] = IRS_SDR_solving(H_t,A_sensing,R_K,R,H_2,H_1,L,N,K,Gamma,sigma,Inmax,errin,Phi,h_K,ori_chi)

H_L=cell(1,L);
for l=1:L
    H_L{1,l}=diag(A_sensing(:,l)')*H_t*R*H_t'*diag(A_sensing(:,l));
    H_L{1,l}=[H_L{1,l} zeros(N,1);zeros(1,N) 0];%得到对应的\bar{H}_l
end


W_K = cell(1,K);

G_K=cell(1,K);
tilde_R_K=cell(1,K);
for k=1:K
    G_K{1,k} = diag(H_1(:,k)')*H_t;
    tilde_R_K{1,k}=(1+Gamma^(-1))*R_K(:,:,k)-R;%得到\tilde{R}_k
    
    temp1=G_K{1,k};temp2=tilde_R_K{1,k};
    W_K{1,k} = [temp1*temp2*temp1' temp1*temp2*H_2(:,k);%得到W_k
        H_2(:,k)'*temp2*temp1'  H_2(:,k)'*temp2*H_2(:,k)];
end

%%
% 调试



%%
%初始化V_0,epsilon,rho

cvx_solver mosek_2
cvx_begin quiet
    variable t
    variable V(N+1,N+1)  complex semidefinite
maximize(t)
subject to
    for k=1:K
        1e8*real(trace(W_K{1,k}*V))>=1e8*sigma^2;
    end
    for l=1:L
        real(trace(H_L{1,l}*V))>=t;
    end
    diag(V) == 1;
%     V== hermitian_semidefinite(N+1);
cvx_end

cvx_status;
if(~strcmp(cvx_status,'Solved'))%如果上述问题无解
    v=ones(N,1);
    chi=0;
    return;
end

[Eigenvector,Eigenvalue] = eig(V);
eigen=Eigenvalue(N+1,N+1);
eigenvec=Eigenvector(:,N+1);

%%

chi=0;pre_chi=0;iter=1;epsilon=0;rho_0=0.01;rho=rho_0;pre_V=V;

while((iter <= Inmax && (abs((pre_chi-chi)/(chi))> errin || trace(pre_V)/eigen >=1+errin)) || iter == 1)
    
    
    epsilon=min(1,eigen/trace(pre_V)+rho);

    cvx_solver mosek_2
    cvx_begin quiet
        variable t
        variable V(N+1,N+1)  complex semidefinite
    maximize(t)
    subject to
        for k=1:K
            1e7*real(trace(W_K{1,k}*V))>=1e7*sigma^2;
        end
        for l=1:L
            real(trace(H_L{1,l}*V))>=t;
        end
        diag(V) == 1;
        eigenvec'*V*eigenvec>=epsilon*trace(V);
    %     V== hermitian_semidefinite(N+1);
    cvx_end

    if(~strcmp(cvx_status,'Solved'))%如果上述问题无解
        rho=rho/2;
    else%上述问题有解
        pre_V=V;
        pre_chi=chi;
        chi=t;      
        rho=rho_0;        
    end

    iter = iter + 1;

    [Eigenvector,Eigenvalue] = eig(pre_V);
    eigen=Eigenvalue(N+1,N+1);
    eigenvec=Eigenvector(:,N+1);

    
end

[Eigenvector,Eigenvalue] = eig(pre_V);

v=sqrt(Eigenvalue(N+1,N+1))*Eigenvector(:,N+1);
v=exp(1j*angle(v/v(end)));

for l=1:L
    beamgain(l)=v'*H_L{1,l}*v;
end
chi= min(beamgain);%得到所有波束图当中最小的方向

%如果因为归一化造成误差
if(chi<ori_chi)
    v=conj(diag(Phi));
    chi=ori_chi;
    return;
end
v=v(1:N);


%% 
%调试
% for k=1:K
%     if(real(trace(W_K{1,k}*V))<sigma^2)%不满足约束的情况
%         logic=0
%     end
% end
%%

% 高斯随机化过程
% random_time = 10000; % 高斯随机化过程次数
% 
% max_t = -100;
% max_v = 0;
% [U, Sigma] = eig(V);
% 
% max_eigenvalue=Sigma(N+1,N+1);
% eigen_vec=U(:,N+1);
% v=sqrt(max_eigenvalue)*eigen_vec;
% 
% V=v*v';
% logic=1;
% 
% for k=1:K
%     if(real(trace(W_K{1,k}*V))<sigma^2)%不满足约束的情况
%         logic=0;
%     end
% end
% 
% if(logic==1)
%     beamgain=ones(1,L);%朝向L个方向的波束增益
%     for l=1:L
%         beamgain(l)=v'*H_L{1,l}*v;
%     end
%     min_value= min(beamgain);%得到所有波束图当中最小的方向
%     max_t=min_value;
%     max_v=v;
% end
% 
% for l = 1 : random_time
%     r = sqrt(2) / 2 * (randn(N+1, 1) + 1j * randn(N+1, 1));
%     v = U * Sigma^(0.5) * r;
% 
% %     abs(v)
% %     V=v*v'
% 
%     v = exp(1j * angle(v / v(end)));
% 
%     V=v*v';
%     logic=1;
% 
%     for k=1:K
%         if(real(trace(W_K{1,k}*V))<sigma^2)%不满足约束的情况
%             logic=0;
%         end
%     end
% 
%     if(logic==1)
%         beamgain=ones(1,L);%朝向L个方向的波束增益
%         for l=1:L
%             beamgain(l)=v'*H_L{1,l}*v;
%         end
%         min_value= min(beamgain);%得到所有波束图当中最小的方向
% 
%         if (min_value > max_t)
%             max_v = v;
%             max_t = min_value;
%         end
%     end
% end

%%
%调试
% V=max_v*max_v'
% for l=1:5
%     beamgain(l)=max_v'*H_L{1,l}*max_v;
% end
% min_value=min(beamgain)



% v = exp(1j * angle(max_v / max_v(end)));
% 
% for l=1:5
%     beamgain(l)=v'*H_L{1,l}*v;
% end
% min_value=min(beamgain)



% 
% v = v(1 : N);
%   Phi=diag(conj(v));
% v=[v; 1]
% %调试
%       
% 
% for l=1:5
%     A_theta(:,:,l)=H_t'*Phi'*A_sensing(:,l)*A_sensing(:,l)'*Phi*H_t;
%     trace(A_theta(:,:,l)*R)
%     A_sensing(:,l)'*Phi*H_t*R*H_t'*Phi'*A_sensing(:,l)
%     v'*H_L{1,l}*v
% end

% max_v=exp(1j * angle(max_v / max_v(end)));

end
