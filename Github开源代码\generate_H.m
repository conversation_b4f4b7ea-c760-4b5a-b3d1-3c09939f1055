%%H_1:表示IRS到用户的信道矩阵，H_1的第k列表示到第k个用户的信道矩阵h_1,k
%g_0：表示参考坐标1m处的信道衰弱
%BS_CU：表示BS到用户的距离
%IRS_CU：表示IRS到用户的距离
%BI_distance：表示BS到IRS的距离
%num_CU：表示用户的数量K
%T：表示基站天线坐标，2*M
%h_K：表示BS到用户k的等效信道矩阵，大小为M*K;
% 生成信道矩阵
function [H_BS_IRS,h_K,H_1,H_2,F_IRS,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS,theta_BS_CU_t,phi_BS_CU_t,theta_BS_t,phi_BS_t,G_IRS_CU] = generate_H(g_0,BS_CU,IRS_CU,BI_distance,num_CU,alpha_Rician,alpha_Rayleigh,L,IRS_location,T,lambda,Phi,M,N)

    K=num_CU;
 
    %计算BS-CU的Sigma,IRS-CU的Sigma,BS-IRS的Sigma
    Sigma_Rayleigh=ones(L,L,num_CU);Sigma_Rician=ones(L,L,num_CU);Sigma=ones(L,L);
    for i=1:num_CU
        Sigma_Rayleigh(:,:,i) = generate_Sigma(g_0,BS_CU(i),alpha_Rayleigh,L,1);
        Sigma_Rician(:,:,i) = generate_Sigma(g_0,IRS_CU(i),alpha_Rician,L,2);
    end
    Sigma_Rician_IRS = generate_Sigma(g_0,BI_distance,alpha_Rician,L,2);


    % 随机收发角度[0， pi]均匀分布
    theta_BS_t = pi*rand(L,1);
    phi_BS_t = pi*rand(L,1);         % BS to IRS 发射方位角和俯仰角
    
    % IRS 接受角度
    theta_IRS_r = pi*rand(L,1);
    phi_IRS_r = pi*rand(L,1);         % BS to IRS 接收方位角和俯仰角
 
    
    F_IRS = cal_FRV_Matrix(IRS_location,theta_IRS_r,phi_IRS_r,lambda);   % IRS接收FRM

    G_BS_IRS = cal_FRV_Matrix(T,theta_BS_t,phi_BS_t,lambda);   % BS-IRS信道的FRM

    %BS直接到第k个用户的发射FRM
    G_BS_CU=ones(L,M,K);theta_BS_CU_t=ones(L,K);phi_BS_CU_t=ones(L,K);
    %IRS到第k个用户的发射FRM
    G_IRS_CU=ones(L,N,K);theta_IRS_CU_t=ones(L,K);phi_IRS_CU_t=ones(L,K);
    for k=1:K
        theta_BS_CU_t(:,k) = pi*rand(L,1);% BS to 到第k个 CU 发射方位角和俯仰角
        phi_BS_CU_t(:,k) = pi*rand(L,1);         
        G_BS_CU(:,:,k) =  cal_FRV_Matrix(T,theta_BS_CU_t(:,k),phi_BS_CU_t(:,k),lambda);   % BS-CU信道的发射FRM

        theta_IRS_CU_t(:,k) = pi*rand(L,1);% BS to 到第k个 CU 发射方位角和俯仰角
        phi_IRS_CU_t(:,k)=pi*rand(L,1);
        G_IRS_CU(:,:,k) = cal_FRV_Matrix(IRS_location,theta_IRS_CU_t(:,k),phi_IRS_CU_t(:,k),lambda);   % IRS-CU信道的FRM
    end

    

    % 信道矩阵
    H_BS_IRS =  F_IRS' * Sigma_Rician_IRS * G_BS_IRS;     % BS to IRS

    H_1=ones(N,K);% IRS-CU 的信道
    H_2=ones(M,K);% BS-CU 的信道
    h_K=ones(M,K);% BS-CU 的等效信道
    for k=1:K
        H_2(:,k)=(ones(1,L)*Sigma_Rayleigh(:,:,k)*G_BS_CU(:,:,k))';%BS直接到第k个用户的信道
        H_1(:,k)=(ones(1,L)*Sigma_Rician(:,:,k)*G_IRS_CU(:,:,k))';%IRS到第k个用户的信道
        h_K(:,k)=(H_1(:,k)'*Phi*H_BS_IRS+H_2(:,k)')';
    end

end