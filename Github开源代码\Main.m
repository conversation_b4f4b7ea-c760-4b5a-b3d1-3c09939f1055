clc
clear all
close all
rng(15);
%%
% parameter

f = 2.4e9;                   % 频率
lambda = 3e8/f;             % 波长,单位米
M = 8;                      % BS移动天线数

d_step_1=1e-3;                % 天线移动的步进步长
d_step_2=2e-3;                % 天线移动的步进步长

d = lambda/2;
num_CU=2;                   % 需要服务的用户数量
Gamma=10^(10/10);            %用户处信噪比要求10dB
A_BS=6;                     %表示BS移动天线范围是6\lambda*6\lambda的区域
theta_sensing = [-60 -30 0 30 60];   % sensing direction
% theta_sensing = [-60 ];
N = 16;                     % number of RIS elements
theta = -90:0.25:90; % angle range，采样间隔为0.25
n = 0:N-1;
A_sensing = exp(-1i*2*pi*d/lambda.*n'.*sind(theta_sensing));% steering vector of sen direction,N*L大小，L为感兴趣感知方向数量
A=exp(-1i*2*pi*d/lambda.*n'.*sind(theta));%所有目标方向

BS_IRS_alpha = 2.5;         % BS_IRS路径损耗因子
IRS_CU_alpha = 2.5;         % BS_CU路径损耗因子
BS_CU_alpha = 3.5;          % BS_CU路径损耗因子，存在阻碍系数更大


g_0 = -40;                 % 参考点1m平均信道增益为-20dB
Pmax_dbm = 40;                  % BS最大发射功率/dbm
Pmax = 10^(Pmax_dbm/10)/(1e3);  % BS最大发射功率/W
noise_power_dbm = -80;          % 用户接受处噪声平均功率/dbm
noise_power = 10^(noise_power_dbm/10)/(1e3);
D = 0.5*lambda;                 % BS移动天线之间的距离限制
L = 4;                          % 多径数量
Nmax = 150;                     % 外部迭代次数
Inmax = 500;                    % 内部迭代次数【MA位置优化】
simulationtime = 350;             % 仿真次数
errout = 10^(-3);%外循环的误差界限
errin_MA = 10^(-3);%MA优化的误差界限
errin_IRS = 10^(-7);%IRS优化的误差界限
%%
%仿真不同利用天线空间自由度算法随天线移动空间距离变化
P_Candidate=34:2:44;

% %simulate
[BS_CU,IRS_CU]=generate_CU_location(num_CU);  % CU到BS，IRS之间的距离
BI_distance=20;


Matrix_chi_CON_N16_L=zeros(simulationtime,length(P_Candidate));
Matrix_PS_chi_N16_dstep1=zeros(simulationtime,length(P_Candidate));
Matrix_PS_chi_N16_dstep2=zeros(simulationtime,length(P_Candidate));

Matrix_chi_CON_N24_L=zeros(simulationtime,length(P_Candidate));
Matrix_PS_chi_N24_dstep1=zeros(simulationtime,length(P_Candidate));
Matrix_PS_chi_N24_dstep2=zeros(simulationtime,length(P_Candidate));



chi_CON_N16_L=zeros(1,length(P_Candidate));
chi_CON_N24_L=zeros(1,length(P_Candidate));


%计算完整算法的收敛性
for simul=1:simulationtime
    simul

    ori_T = generate_MA_init_positions(M,lambda,A_BS); %生成2*M的矩阵，表示初始的MA的位置
    T=ori_T;

    N=16
    n = 0:N-1;
    A_sensing = exp(-1i*2*pi*d/lambda.*n'.*sind(theta_sensing));% steering vector of sen direction,N*L大小，L为感兴趣感知方向数量
    A=exp(-1i*2*pi*d/lambda.*n'.*sind(theta));%所有目标方向
    IRS_location= generate_UPA_positions(N, lambda); %形成一个2*N的矩阵，表示IRS的位置
%%
    Phi=diag(ones(N,1));

    %生成信道矩阵    
    [H_BS_IRS,h_K,H_1,H_2,F_IRS,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS,theta_BS_CU_t,phi_BS_CU_t,theta_BS_t,phi_BS_t,G_IRS_CU] = generate_H(g_0,BS_CU,IRS_CU,BI_distance,num_CU,BS_IRS_alpha,BS_CU_alpha,L,IRS_location,T,lambda,Phi,M,N);

    temp1=zeros(length(P_Candidate),1);temp2=zeros(length(P_Candidate),1);temp3=zeros(length(P_Candidate),1);temp4=zeros(length(P_Candidate),1);temp5=zeros(length(P_Candidate),1);temp6=zeros(length(P_Candidate),1);
    for Pindex=1:length(P_Candidate)
        Pmax_dbm=P_Candidate(Pindex);
        Pmax = 10^(Pmax_dbm/10)/(1e3);

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% PS，实际天线移动方案 %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        T = generate_MA_init_positions(M,lambda,A_BS); %生成2*M的矩阵，表示初始的天线的位置
        %更新信道信息
        [H_BS_IRS,h_K,H_1,H_2] = cal_H(T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,num_CU,N);
        %AO算法，迭代优化
        iter=1;cur_chi=0;pre_chi=0;R=zeros(M,M);R_K=zeros(M,M,num_CU);
        while( (iter <= Nmax&&abs((cur_chi-pre_chi)/(cur_chi))>errout) || iter == 1)
            iter
            pre_chi = cur_chi;

            %基站发送矩阵优化
            [R,R_K,chi] =BS_SDR_solving(theta_sensing, Phi,h_K,H_BS_IRS, A_sensing,Pmax,sqrt(noise_power),Gamma,M,length(theta_sensing),num_CU,R,R_K,pre_chi);
            chi
            %IRS 相移优化
            [v,chi] = IRS_SDR_solving(H_BS_IRS,A_sensing,R_K,R,H_2,H_1,length(theta_sensing),N,num_CU,Gamma,sqrt(noise_power),Inmax,errin_IRS,Phi,h_K,chi);
            chi
            Phi=diag(conj(v));
            %Practical Scenario 离散移动距离条件下优化MA
            [T,chi]=Antenna_Position_Design(Inmax,errin_MA,R,Gamma,ori_T,T,A_sensing,Phi,F_IRS,Sigma_Rician_IRS,H_1,Sigma_Rayleigh,lambda,M,num_CU,R_K,L,theta_BS_CU_t,phi_BS_CU_t,theta_BS_t,phi_BS_t,length(theta_sensing),sqrt(noise_power),A_BS);
            T
            chi
            %更新信道信息
            [H_BS_IRS,h_K,H_1,H_2] = cal_H(T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,num_CU,N);
            cur_chi=chi;
            iter = iter + 1;
        end
%%
        % temp1保存没有离散化的数据
        temp1(Pindex)=chi;

%% 
        % 计算1mm离散的MA点位置
        dis_T=round((T-ori_T)/d_step_1)*d_step_1+ori_T;
        % 计算离散点条件下波束增益的值
        [H_BS_IRS,h_K,H_1,H_2] = cal_H(dis_T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,num_CU,N);
        [~,~,chi] = BS_SDR_solving(theta_sensing, Phi,h_K,H_BS_IRS, A_sensing,Pmax,sqrt(noise_power),Gamma,M,length(theta_sensing),num_CU,R,R_K,pre_chi);
        temp2(Pindex)=chi;

        % 计算2mm离散的MA点位置
        dis_T=round((T-ori_T)/d_step_2)*d_step_2+ori_T;
        % 计算离散点条件下波束增益的值
        [H_BS_IRS,h_K,H_1,H_2] = cal_H(dis_T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,num_CU,N);
        [~,~,chi] = BS_SDR_solving(theta_sensing, Phi,h_K,H_BS_IRS, A_sensing,Pmax,sqrt(noise_power),Gamma,M,length(theta_sensing),num_CU,R,R_K,pre_chi);
        temp3(Pindex)=chi;

    end
    %记录没有离散化的值
    Matrix_chi_CON_N16_L(simul,:)=temp1;
    Matrix_PS_chi_N16_dstep1(simul,:)=temp2;
    Matrix_PS_chi_N16_dstep2(simul,:)=temp3;

%%
    N=24
    n = 0:N-1;
    A_sensing = exp(-1i*2*pi*d/lambda.*n'.*sind(theta_sensing));% steering vector of sen direction,N*L大小，L为感兴趣感知方向数量
    A=exp(-1i*2*pi*d/lambda.*n'.*sind(theta));%所有目标方向
    IRS_location= generate_UPA_positions(N, lambda); %形成一个2*N的矩阵，表示IRS的位置

    Phi=diag(ones(N,1));
    T = generate_UPA_positions(M, lambda);  %生成2*M的矩阵，表示初始的天线的位置
    %生成信道矩阵    
    [H_BS_IRS,h_K,H_1,H_2,F_IRS,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS,theta_BS_CU_t,phi_BS_CU_t,theta_BS_t,phi_BS_t,G_IRS_CU] = generate_H(g_0,BS_CU,IRS_CU,BI_distance,num_CU,BS_IRS_alpha,BS_CU_alpha,L,IRS_location,T,lambda,Phi,M,N);

    temp1=zeros(1,length(P_Candidate));temp2=zeros(1,length(P_Candidate));temp3=zeros(1,length(P_Candidate));
    for Pindex=1:length(P_Candidate)
        Pmax_dbm=P_Candidate(Pindex);
        Pmax = 10^(Pmax_dbm/10)/(1e3);

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% PS，实际天线移动方案 %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        T = generate_MA_init_positions(M,lambda,A_BS); %生成2*M的矩阵，表示初始的天线的位置
        %更新信道信息
        [H_BS_IRS,h_K,H_1,H_2] = cal_H(T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,num_CU,N);
        %AO算法，迭代优化
        iter=1;cur_chi=0;pre_chi=0;R=zeros(M,M);R_K=zeros(M,M,num_CU);
        while( (iter <= Nmax&&abs((cur_chi-pre_chi)/(cur_chi))>errout) || iter == 1)
            iter
            pre_chi = cur_chi;

            %基站发送矩阵优化
            [R,R_K,chi] =BS_SDR_solving(theta_sensing, Phi,h_K,H_BS_IRS, A_sensing,Pmax,sqrt(noise_power),Gamma,M,length(theta_sensing),num_CU,R,R_K,pre_chi);
            chi
            %IRS 相移优化
            [v,chi] = IRS_SDR_solving(H_BS_IRS,A_sensing,R_K,R,H_2,H_1,length(theta_sensing),N,num_CU,Gamma,sqrt(noise_power),Inmax,errin_IRS,Phi,h_K,chi);
            chi
            Phi=diag(conj(v));
            %Practical Scenario 离散移动距离条件下优化MA
            [T,chi]=Antenna_Position_Design(Inmax,errin_MA,R,Gamma,ori_T,T,A_sensing,Phi,F_IRS,Sigma_Rician_IRS,H_1,Sigma_Rayleigh,lambda,M,num_CU,R_K,L,theta_BS_CU_t,phi_BS_CU_t,theta_BS_t,phi_BS_t,length(theta_sensing),sqrt(noise_power),A_BS);
            T
            chi
            %更新信道信息
            [H_BS_IRS,h_K,H_1,H_2] = cal_H(T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,num_CU,N);
            cur_chi=chi;
            iter = iter + 1;
        end

        % temp4保存没有离散化的数据
        temp4(Pindex)=chi;
        %% 
        % 计算1mm离散的MA点位置
        dis_T=round((T-ori_T)/d_step_1)*d_step_1+ori_T;
        % 计算离散点条件下波束增益的值
        [H_BS_IRS,h_K,H_1,H_2] = cal_H(dis_T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,num_CU,N);
        [~,~,chi] = BS_SDR_solving(theta_sensing, Phi,h_K,H_BS_IRS, A_sensing,Pmax,sqrt(noise_power),Gamma,M,length(theta_sensing),num_CU,R,R_K,pre_chi);
        temp5(Pindex)=chi;

        % 计算2mm离散的MA点位置
        dis_T=round((T-ori_T)/d_step_2)*d_step_2+ori_T;
        % 计算离散点条件下波束增益的值
        [H_BS_IRS,h_K,H_1,H_2] = cal_H(dis_T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,num_CU,N);
        [~,~,chi] = BS_SDR_solving(theta_sensing, Phi,h_K,H_BS_IRS, A_sensing,Pmax,sqrt(noise_power),Gamma,M,length(theta_sensing),num_CU,R,R_K,pre_chi);
        temp6(Pindex)=chi;
 

    end

    %记录没有离散化的值
    Matrix_chi_CON_N24_L(simul,:)=temp4;
    Matrix_PS_chi_N24_dstep1(simul,:)=temp5;
    Matrix_PS_chi_N24_dstep2(simul,:)=temp6;

    
end


%%
chi_CON_N16_L=sum(Matrix_chi_CON_N16_L,1)/simulationtime;
PS_chi_N16_dstep1=sum( Matrix_PS_chi_N16_dstep1,1)/simulationtime;
PS_chi_N24_dstep1=sum( Matrix_PS_chi_N24_dstep1,1)/simulationtime;


chi_CON_N24_L=sum(Matrix_chi_CON_N24_L,1)/simulationtime;
PS_chi_N16_dstep2=sum( Matrix_PS_chi_N16_dstep2,1)/simulationtime;
PS_chi_N24_dstep2=sum( Matrix_PS_chi_N24_dstep2,1)/simulationtime;









plot(1:length(P_Candidate),FPA_chi_N16,'color','#f05c28','linewidth',1.2,'LineStyle','--','Marker','s');hold on;
plot(1:length(P_Candidate),MA_chi_N16,'color','#248cc4','linewidth',1.2,'LineStyle','--','Marker','^');hold on;
plot(1:length(P_Candidate),EAS_chi_N16,'color','#7D2E8F','linewidth',1.2,'LineStyle','--','Marker','*');hold on;
plot(1:length(P_Candidate),PS_chi_N16_dstep1,'color','#78AB30','linewidth',1.2,'LineStyle','--','Marker','^');hold on;

plot(1:length(P_Candidate),FPA_chi_N24,'color','#f05c28','linewidth',1,'LineStyle','-','Marker','s');hold on;
plot(1:length(P_Candidate),MA_chi_N24,'color','#248cc4','linewidth',1,'LineStyle','-','Marker','^');hold on;
plot(1:length(P_Candidate),EAS_chi_N24,'color','#7D2E8F','linewidth',1,'LineStyle','-','Marker','*');hold on;
plot(1:length(P_Candidate),PS_chi_N24_dstep1,'color','#78AB30','linewidth',1,'LineStyle','-','Marker','^');hold on;

xlabel('Total transmit power(dBm)','Interpreter', 'tex')
ylabel('Minimum beampattern gain','Interpreter', 'tex')

grid on;
set(gca, 'GridLineStyle', '--', 'GridColor', 'k', 'GridAlpha', 0.2);  % 黑色虚线，半透明
% xlim([1 length(P_Candidate)])
% xticks([1,3,5,7,9,11,13,15]);  % 设置刻度位置
xticklabels({"34",'36', '38', '40', '42', '44'});  % 设置刻度标签
h=legend('FPA,$N=16$','MA,$N=16$','EAS,$N=16$','PS,$N=16$','FPA,$N=24$','MA,$N=24$','EAS,$N=24$','PS,$N=24$'); %latex分式
set(h,'Interpreter','latex') %设置legend为latex解释器显示分式
% %
% 
% 
% 绘制波束图对比图
% figure;plot(theta,Beampattern(1,:),'color','#248cc4','linewidth',1);xlim([-90 90]);hold on;%蓝色
% plot(theta,Beampattern(2,:),'color','#78AB30','linewidth',1);xlim([-90 90]);hold on;%绿色
% plot(theta,Beampattern(3,:),'color','#7D2E8F','linewidth',1);xlim([-90 90]);hold on;%紫色
% plot(theta,Beampattern(4,:),'color','#f05c28','linewidth',1,'LineStyle','--' );%橙色
% ylim([0 1.3]);
% xticks([-90,-60,-30,0,30,60,90]);  % 设置刻度位置
% xticklabels({"-90",'-60', '-30', '0', '30', '60',"90"});  % 设置刻度标签
% xlabel("Angle(Degree)");ylabel("Beampattern Gain");grid on;
% h=legend('Algorithm w/ MA,$N=16$','Algorithm w/o MA,$N=16$','Algorithm w/ MA,$N=24$','Algorithm w/o MA,$N=24$'); %latex分式
% set(h,'Interpreter','latex') %设置legend为latex解释器显示分式