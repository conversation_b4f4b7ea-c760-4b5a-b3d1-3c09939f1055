%H_1:表示IRS到用户的信道矩阵，H_1的第k列表示到第k个用户的信道矩阵h_1,k Sigma_Rayleigh:表示从BS直接到用户的路径相应
%\Sigma,大小为 num_CU*L*L
%F_IRS:表示从BS到IRS信道的接受矩阵
%Sigma_Rician_IRS:表示BS到IRS信道的路径响应矩阵
%ori_T表示天线移动的初始点
function [optimal_T,cur_chi]=Antenna_Position_Design(Inmax,errin,R,Gamma,ori_T,T,A_theta,Phi,F_IRS,Sigma_Rician_IRS,H_1,Sigma_Rayleigh,lambda,M,K,R_K,L_t,theta_BS_CU_t,phi_BS_CU_t,theta_BS_t,phi_BS_t,L,sigma,A_BS)




for n= 1:M   % 对第m根天线位置进行优化

    tn = T(:,n); %取出第n个天线坐标


    % 使用 repmat 扩展 R 至 M*M*K，然后执行减法 注意：repmat(B, [1, 1, K]) 将 R 复制 K 次沿第三维
    R_expanded = repmat(R, [1, 1, K]);
    tilde_R_K = (1+Gamma^(-1))*R_K - R_expanded;

    P=ones(L_t,K);Q=ones(L_t,K);
    for k=1:K
        P(:,k)=Sigma_Rician_IRS'*F_IRS*Phi'*H_1(:,k);
        Q(:,k)=Sigma_Rayleigh(:,:,k)'*ones(L_t,1);
    end

    %计算\tilde a_k,\tilde b_k
    tilde_a=zeros(1,K);tilde_b=zeros(1,K);X=ones(K,M);
    [G_update] = cal_FRV_Matrix(T,theta_BS_t,phi_BS_t,lambda);   % BS向IRS的发射G_t

    for k=1:K
        G_BS_CU_update(:,:,k) =  cal_FRV_Matrix(T,theta_BS_CU_t(:,k),phi_BS_CU_t(:,k),lambda);   % BS-CU信道的发射FRM
        X(k,:)=P(:,k)'*G_update+Q(:,k)'*G_BS_CU_update(:,:,k);
    end
    for k=1:K
        for q=1:M
            if(q~=n)
                tilde_a(k)=tilde_a(k)+tilde_R_K(n,q,k)*X(k,q)';
            end
        end

        for p=1:M
            if(p~=n)
                for q=1:p-1
                    if(q~=n)
                        tilde_b(k)=tilde_b(k)+2*real(tilde_R_K(p,q,k)*X(k,p)*X(k,q)');
                    end
                end
                tilde_b(k)=tilde_b(k)+tilde_R_K(p,p,k)*abs(X(k,p))^2;
            end
        end
    end



    iter = 1;
    pre_chi = 0;
    cur_chi = 0;
    G_update=ones(L_t,M);G_BS_CU_update=ones(L_t,M,K);
    while(iter <= Inmax && abs((cur_chi-pre_chi)/(cur_chi))> errin || iter == 1)


        pre_chi = cur_chi;
        tn_iter = tn;

        %每一次SCA算法的迭代都需要修改G_update，G_BS_CU_update
        [G_update] = cal_FRV_Matrix(T,theta_BS_t,phi_BS_t,lambda);   % BS向IRS的发射G_t
        for k=1:K
            G_BS_CU_update(:,:,k) =  cal_FRV_Matrix(T,theta_BS_CU_t(:,k),phi_BS_CU_t(:,k),lambda);   % BS-CU信道的发射FRM
        end

        C_theta=Phi'*A_theta;
        D_theta=Sigma_Rician_IRS'*F_IRS*C_theta;

        B_1=zeros(L_t,L);B_2=zeros(L_t,L);%B_1,B_2的每一列都存储着b_1,b_2

        for l=1:L
            B_1(:,l)=(2*R(n,n)*G_update(:,n)'*D_theta(:,l)*D_theta(:,l)').';
            temp=0;
            for q=1:M
                if(q~=n)
                    temp=temp+R(n,q)*G_update(:,q)';
                end
            end
            B_2(:,l)=(2*temp*D_theta(:,l)*D_theta(:,l)').';
        end
        B=B_1+B_2;
        % 计算矩阵 B 中每个元素的幅值
        B_abs = abs(B);
        % 计算每一列元素的幅值和
        col_sum = sum(B_abs, 1);

        %%
        %计算A(t_n)
        A_tn=zeros(L_t,L_t);
        for q=1:M
            if(q~=n)
                A_tn=A_tn+R(n,q)*G_update(:,n)*G_update(:,q)'+ conj(R(n,q))*G_update(:,q)*G_update(:,n)';
            end
        end
        Delta_1=8*pi^2/(lambda^2)*col_sum;%Delta_1中每一列都是delta，对应的是L个不同角度

        %计算B_n的值,没有问题
        temp=zeros(L_t,L_t);
        for k=1:M
            if(k~=n)
                for q=1:k-1
                    if(q~=n)
                        temp=temp+R(k,q)*G_update(:,k)*G_update(:,q)'+conj(R(k,q))*G_update(:,q)*G_update(:,k)';
                    end
                end
                temp=temp+R(k,k)*G_update(:,k)*G_update(:,k)';
            end
        end
        B_n=temp;





        x_angle = sin(theta_BS_t).* cos(phi_BS_t);
        y_angle = cos(theta_BS_t);
        angle_gather = [x_angle,y_angle];
        diff_dis = angle_gather * tn_iter;



        bar_I=ones(1,L);
        Sense_R_const=ones(1,L);Sense_L_const=ones(1,L);gradient_sen=ones(2,L);
        for l=1:L
            gradient_sen(:,l) = cal_gradient_sen(B(:,l),theta_BS_t,phi_BS_t,lambda,tn_iter);
            bar_I(l)=real(B(:,l).'*G_update(:,n));%计算得到bar_I(tn)在tn_iter处的值
            Sense_L_const(l)= bar_I(l)-Delta_1(l)/2*tn_iter'*tn_iter-gradient_sen(:,l)'*tn_iter;
            Sense_R_const(l)=-D_theta(:,l)'*B_n*D_theta(:,l)+R(n,n)*(abs(D_theta(:,l)'*G_update(:,n)))^2;
        end

        %%
        %用户处信噪比限制

        %         Delta_2是1*K的矩阵，第k个元素存放的是\tilde \delta_k
        Delta_2=ones(1,K);


        for k=1:K
            X(k,:)=P(:,k)'*G_update+Q(:,k)'*G_BS_CU_update(:,:,k);
        end
        tilde_I=ones(1,K);gradient_com=ones(2,K);
        for k=1:K
            [gradient_com(:,k),Delta_2(k)]=cal_gradient_com(theta_BS_t,theta_BS_CU_t(:,k),phi_BS_t,phi_BS_CU_t(:,k),tn_iter,P(:,k),Q(:,k),lambda,L_t,tilde_a(k),tilde_R_K(n,n,k));
            tilde_I(k)=2*real(tilde_a(k)*X(k,n))+tilde_R_K(n,n,k)*abs(X(k,n))^2;
            Com_L_const(k)= tilde_I(k)-Delta_2(k)/2*tn_iter'*tn_iter-gradient_com(:,k)'*tn_iter;
            Com_R_const(k)=sigma^2-tilde_b(k);
        end



        %%
        %用户距离限制

        %构成用户距离限制的线性矩阵Ax>=b
        A = zeros(M-1,2);
        b = zeros(M-1,1);
        num = 0;
        D=lambda/2;
        for k = 1: M
            if(k ~= n)
                num = num + 1;
                tn_k = T(:,k);
                A(num,:) = 1/(norm(tn_iter - tn_k))*(tn_iter-tn_k)';
                b(num) = D + 1/(norm(tn_iter - tn_k))*(tn_iter-tn_k)' * tn_k;
            end
        end

        %%
        % 调试
        % g =  cal_FRV_Matrix(T(:,1),theta_BS_CU_t(:,2),phi_BS_CU_t(:,2),lambda)  % BS-CU信道的发射FRM
        %
        %         X(2,1)
        %         X(2,1)=P(:,2)'*G_update(:,1)+Q(:,2)'*G_BS_CU_update(:,1,2)
        %         x_21
        %         t=tn_iter;
        %
        %         g_update=cal_FRV_Matrix(t,theta_BS_t,phi_BS_t,lambda);
        %         g_k_update=cal_FRV_Matrix(t,theta_BS_CU_t(:,2),phi_BS_CU_t(:,2),lambda);
        %         x_21=P(:,2)'*g_update+Q(:,2)'*g_k_update
        %
        %
        %         tilde_I(2)=2*real(tilde_a(2)*X(2,n)+tilde_R_K(n,n,2)*abs(X(2,n))^2)
        %         2*real(tilde_a(2)*x_21+tilde_R_K(1,1,2)*X(2,1)'*x_21)
        %
        %         Com_L_const(2)= tilde_I(2)-Delta_2(2)/2*tn_iter'*tn_iter-gradient_com(:,2)'*tn_iter
        %
        %         -Delta_2(2)/2*t'*t+(gradient_com(:,2)+Delta_2(2)*tn_iter)'*t+Com_L_const(2)
        %
        %
        %         2*real(tilde_a(2)*x_21+tilde_R_K(1,1,2)*X(2,1)'*x_21)

        %%
        %  感知部分放缩代码，没有问题
        % t=tn_iter+[0.0006;-0.0001];
        % t=tn_iter+0.0001;
        % [g_update] = cal_FRV_Matrix(t,theta_BS_t,phi_BS_t,lambda)   % BS向IRS的发射G_t
        % bar_I=real(B(:,1).'*g_update)
        % 
        % -Delta_1(1)/2*t'*t+(gradient_sen(:,1)+Delta_1(1)*tn_iter)'*t+Sense_L_const(1)

        %%
        % %  通信部分放缩代码，没有问题
        % test_t=tn_iter;
        % g_update=cal_FRV_Matrix(test_t,theta_BS_t,phi_BS_t,lambda);
        % g_k_update=cal_FRV_Matrix(test_t,theta_BS_CU_t(:,2),phi_BS_CU_t(:,2),lambda);
        %
        % G_update=cal_FRV_Matrix(T,theta_BS_t,phi_BS_t,lambda);
        % % G_2_update=cal_FRV_Matrix(T,theta_BS_CU_t(:,2),phi_BS_CU_t(:,2),lambda);
        % %
        % x_21=P(:,2)'*g_update+Q(:,2)'*g_k_update
        % 2*real(tilde_a(2)*x_21)+tilde_R_K(1,1,2)*x_21'*x_21
        % % % (P(:,2)'*G_update+Q(:,2)'*G_2_update)*tilde_R_K(:,:,2)*(P(:,2)'*G_update+Q(:,2)'*G_2_update)'
        % % %
        % n=1;k=2;
        % % %
        % % 2*real(tilde_a(2)*x_21+tilde_R_K(1,1,2)*X(2,1)'*x_21)
        % % %
        % tilde_I(k)
        % %
        % %
        % -Delta_2(2)/2*t'*t+(gradient_com(:,2)+Delta_2(2)*tn_iter)'*t+Com_L_const(2)
        %
        %
        %  -Delta_2(k)/2*test_t'*test_t+(gradient_com(:,k)+Delta_2(k)*tn_iter)'*test_t+Com_L_const(k)

        %%
        % %调试Delta_2(1)

        % test_t=tn_iter-0.0001;
        % g_update=cal_FRV_Matrix(test_t,theta_BS_t,phi_BS_t,lambda);
        % g_1_update=cal_FRV_Matrix(test_t,theta_BS_CU_t(:,1),phi_BS_CU_t(:,1),lambda);
        % T(:,1)=test_t;
        % %
        % G_update=cal_FRV_Matrix(T,theta_BS_t,phi_BS_t,lambda);
        % G_1_update=cal_FRV_Matrix(T,theta_BS_CU_t(:,1),phi_BS_CU_t(:,1),lambda);
        % %
        % x_11=P(:,1)'*g_update+Q(:,1)'*g_1_update;
        % %
        % %(34)式左侧的tilde_{I},m=1,k=1
        % tilde_I=tilde_R_K(1,1,1)*x_11'*x_11+2*real(tilde_a(1)*x_11)
        % %(39)式泰勒展开表达式
        % Taylor=-Delta_2(1)/2*test_t'*test_t+(gradient_com(:,1)+Delta_2(1)*tn_iter)'*test_t+Com_L_const(1)

        %接下来我们简单比较这两个式子的大小关系



        % (P(:,1)'*G_update+Q(:,1)'*G_1_update)*tilde_R_K(:,:,1)*(P(:,1)'*G_update+Q(:,1)'*G_1_update)';


        %
        % %
        % % 2*real(tilde_a(2)*x_21+tilde_R_K(1,1,2)*X(2,1)'*x_21)
        %
        %
        % tilde_I(1)+tilde_b(1)
        %
        % %(51式)
        % -Delta_2(1)/2*test_t'*test_t+(gradient_com(:,1)+Delta_2(1)*tn_iter)'*test_t+Com_L_const(1)+tilde_b(1)
        %
        %
        % 
        % 2*real(tilde_a(1)*x_11)+tilde_R_K(1,1,1)*x_11'*x_11
        % 
        % -Delta_2(1)/2*test_t'*test_t+(gradient_com(:,1)+Delta_2(1)*tn_iter)'*test_t+Com_L_const(1)



        %%
        %tilde_a，tilde_b，X的计算都没有问题
        % (P(:,1)'*G_update+ Q(:,1)'*G_BS_CU_update(:,:,1))*tilde_R_K(:,:,1)*(G_update'*P(:,1)+G_BS_CU_update(:,:,1)'*Q(:,1))
        %
        % 2*real(tilde_a(1)*X(1,1)+tilde_R_K(1,1,1)*X(1,1)'*X(1,1))+tilde_b(1)-tilde_R_K(1,1,1)*abs(X(1,1))^2

        % %检查放缩本身有没有问题
        % t=tn_iter+0.0001;
        % g_update=cal_FRV_Matrix(t,theta_BS_t,phi_BS_t,lambda);
        % g_k_update=cal_FRV_Matrix(t,theta_BS_CU_t(:,2),phi_BS_CU_t(:,2),lambda);
        % x_21=P(:,2)'*g_update+Q(:,2)'*g_k_update
        %
        % 2*real(tilde_a(2)*X(2,1))+tilde_R_K(n,n,2)*abs(X(2,n))^2
        % %原始表达式
        % tilde_R_K(1,1,2)*abs(x_21)^2+2*real(tilde_a(2)*x_21)
        % % %放缩表达式
        % % 2*real(tilde_a(2)*x_21+tilde_R_K(1,1,2)*X(2,1)'*x_21)-tilde_R_K(1,1,2)*abs(X(2,1))^2
        %
        % -Delta_2(2)/2*t'*t+(gradient_com(:,2)+Delta_2(2)*tn_iter)'*t+Com_L_const(2)

        % %计算BS-IRS传输线路的路径差,diff_dis_BS_t 为Lt*1的列向量
        % x_angle_BS_t = sin(theta_BS_t).* cos(phi_BS_t);
        % y_angle_BS_t = cos(theta_BS_t);
        % angle_gather_BS_t = [x_angle_BS_t,y_angle_BS_t];
        % diff_dis_BS_t = angle_gather_BS_t * tn_iter;
        %
        % %计算BS-CU传输线路的路径差,diff_dis_BS_t 为Lt*1的列向量
        % x_angle_BS_CU_t = sin(theta_BS_CU_t(:,2)).* cos(phi_BS_CU_t(:,2));
        % y_angle_BS_CU_t = cos(theta_BS_CU_t(:,2));
        % angle_gather_BS_CU_t = [x_angle_BS_CU_t,y_angle_BS_CU_t];
        % diff_dis_BS_CU_t = angle_gather_BS_CU_t * tn_iter;
        %
        % abs(P(:,2)'*g_update)^2
        % p=P(:,2);q=Q(:,2);
        % Lt=length(g_update);
        %
        % temp1=0;
        % for m=1:Lt
        %    for n=1:m-1
        %        temp1=temp1+2*abs(p(m))*abs(p(n))*cos(2*pi/lambda*(diff_dis_BS_t(m)-diff_dis_BS_t(n))+angle(p(n))-angle(p(m)));
        %    end
        % end
        % temp1=temp1+sum(abs(p).^2)
        %
        % 2*real(p'*g_update*g_k_update'*q)
        %
        % temp1=0;
        % for m=1:Lt
        %    for n=1:Lt
        %        temp1=temp1+2*abs(p(m))*abs(q(n))*cos(2*pi/lambda*(diff_dis_BS_t(m)-diff_dis_BS_CU_t(n))+angle(q(n))-angle(p(m)));
        %    end
        % end
        % temp1
        %%
        %CVX优化
        cvx_solver  mosek_2
        cvx_begin quiet
        variable chi
        variable t(2,1)
        maximize(chi)
        subject to
        %感知性能限制
        for l=1:L
            1e3*(-Delta_1(l)/2*t'*t+real((gradient_sen(:,l)+Delta_1(l)*tn_iter)')*t+real(Sense_L_const(l)))>=1e3*(chi+real(Sense_R_const(l)));
        end
        %用户信噪比限制
        for k=1:K
            1e7*(-Delta_2(k)/2*t'*t+real((gradient_com(:,k)+Delta_2(k)*tn_iter)')*t+real(Com_L_const(k)))>=1e7*real(Com_R_const(k));
        end

        %天线距离限制D,线性矩阵限制
        A*t>=b;
        %每个天线移动方形区域限制
        t>=[ori_T(1,n)-1.5*lambda;ori_T(2,n)-1.5*lambda];
        t<=[ori_T(1,n)+1.5*lambda;ori_T(2,n)+1.5*lambda];
        cvx_end
        %%
        %计算实际的chi的值,MATLAB代码本身的bug
        for l=1:L
            temp_chi(l)=-Delta_1(l)/2*t'*t+real((gradient_sen(:,l)+Delta_1(l)*tn_iter)')*t+real(Sense_L_const(l))-real(Sense_R_const(l));
        end
        cur_chi = min(temp_chi);
        iter = iter + 1;
        if(~strcmp(cvx_status,'Solved'))%如果上述问题无解
            T(:,n)=tn;
            cur_chi=pre_chi;
            break;
        end


        T(:,n)= t;  %修改第n个天线坐标
    end
    optimal_T(:,n) = T(:,n);
end


end