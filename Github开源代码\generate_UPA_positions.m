%% 生成UPA天线位置坐标，中心位置在(0,0)
function antenna_positions = generate_UPA_positions(M, lambda)
    [X_num, Y_num] = decompose_even_number(M);%将M分解成接近的一对整数，X_num表示UPA的行数，Y_num表示UPA的列数
    d = lambda / 2;
    antenna_positions = zeros(2, M);
    for row = 1:X_num
        for col = 1:Y_num
            x = (row - 1) * d;
            y = (col - 1) * d;
            antenna_positions(:, (row - 1) * Y_num + col) = [x; y];
        end
    end
    center_position = d/2 * [ X_num - 1, Y_num - 1]';
    antenna_positions =  antenna_positions - center_position;
end