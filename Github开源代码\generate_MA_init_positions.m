function antenna_positions = generate_MA_init_positions(M,lambda,A_BS)
    [X_num, Y_num] = decompose_even_number(M);
    dx = A_BS * lambda / X_num;
    rx = dx/2;
    dy = A_BS * lambda / Y_num;
    ry = dy/2;
    antenna_positions = zeros(2, M);
    for row = 1:X_num
        for col = 1:Y_num
            x = (row - 1) * dx;
            y = (col - 1) * dy;
            antenna_positions(:, (row - 1) * Y_num + col) = [x; y];
        end
    end  
    center_position = [rx,ry]' .* [ X_num - 1, Y_num - 1]';
    antenna_positions =  antenna_positions - center_position;    
end