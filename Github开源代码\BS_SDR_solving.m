%% 输入参数
% BS波束成形优化SDP处理函数
% theta_sensing 表示l个感知角度
% h_K 表示到 K 个用户的复合信道矩阵，即直接路径和经过IRS路径结合
% P_0 表示基站功率限制
% sigma 表示用户处噪声
% Gamma 表示用户信噪比限制
% M 表示可移动天线的数量
% L 表示感知方向数量
%% 输出参数
% R：表示基站输出的信号covariance matrix
% R_K：表示发送第k个用户的covariance matrix，大小为 M*M*K
function [R,R_K,chi] = BS_SDR_solving(theta_sensing, Phi,h_K,H_t, a_sensing,P_0, sigma,Gamma,M,L,K,pre_R,pre_R_K,pre_chi)

A_theta=ones(M,M,L);

for l=1:L
    A_theta(:,:,l)=H_t'*Phi'*a_sensing(:,l)*a_sensing(:,l)'*Phi*H_t;
end

    cvx_solver  mosek_2
    cvx_begin  quiet %不显示求解过程，只输出结果
        variable R(M,M)  hermitian semidefinite
        variable R_K(M,M,K) hermitian semidefinite
        variable t
        
        maximize t;
        subject to
            for k=1:K
                1e5*(1+1/Gamma)*trace(R_K(:,:,k)*h_K(:,k)*h_K(:,k)')>=1e5*(trace(R*h_K(:,k)*h_K(:,k)')+sigma^2);
            end            
%            (1+1/Gamma)*trace(R_K(:,:,1)*h_K(:,1)*h_K(:,1)')>=trace(R*h_K(:,1)*h_K(:,1)')+sigma^2;
            trace(R)<=P_0;
            %每一个发射子矩阵是半正定共轭对称的
            R-sum(R_K,[3]) ==  hermitian_semidefinite(M);
            for l=1:L
                real(trace(A_theta(:,:,l)*R))>=t;
            end     
    cvx_end

    if(~strcmp(cvx_status,'Solved'))%如果上述问题无解
        R=pre_R;
        R_K=pre_R_K;
        chi=pre_chi;
        return;
    end
    
    W_K=ones(M,K);
    for k=1:K
        W_K(:,k)=(h_K(:,k)'*R_K(:,:,k)*h_K(:,k)).^(-0.5)*R_K(:,:,k)*h_K(:,k);
        R_K(:,:,k)=W_K(:,k)*W_K(:,k)';
    end
     
    chi=t;
end