% 计算“大尺度衰落”系数
% g_0表示在参考点d_0处的平均路径增益-30dB,d_0=1m
function [Sigma] = generate_Sigma(g_0,d_k,alpha,L,type)
k=0.5;%Rician因子
if(type==1)% 考虑阴影衰弱的 Rayleigh信道
    g_d = g_0-10*alpha*log10(d_k)+3*randn(1,1); % 接收功率，单位为dB，阴影衰弱标准差为3dB
    g_d_real_value=10^(g_d/10);         % 接收功率，单位为W
    sigma = sqrt(g_d_real_value/L/2)*(randn(L,1)+1j*randn(L,1));  % 每条路径的系数
    Sigma = diag(sigma);                % 几何信道模型
else % Rician 信道
    g_d = g_0-10*alpha*log10(d_k);      % 接收功率，单位为dB
    g_d_real_value=10^(g_d/10);         % 接收功率，单位为W
    sigma = sqrt(g_d_real_value/L/2)*(randn(L,1)+1j*randn(L,1));  % 每条路径的系数
    sigma = sqrt(k/(k+1))*sqrt(g_d_real_value/L) + sqrt(1/(k+1)) * sigma;
    Sigma = diag(sigma);                % 几何信道模型
end
end