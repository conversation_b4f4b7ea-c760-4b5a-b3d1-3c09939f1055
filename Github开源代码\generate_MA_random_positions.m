% 生成随机 M个 MA 位置
% D：表示天线之间的距离限制
function T = generate_MA_random_positions(M,lambda,A_BS,D)
    T = [];
    i = 1;

    %先随机生成一个天线位置，然后再做循环判断是否和某一个天线距离小于天线距离限制D
    while( i <= M)
       flag = 1;
       tm = -A_BS/2 * lambda + (A_BS * lambda).*rand(2,1);      
       for k = 1: i-1
            tm_k = T(:,k);
            if(norm(tm - tm_k) <= D)
                flag = 0;
                break;
            end
       end
       if(flag == 1)
           T(:,i) = tm;
           i = i + 1;
       end  
    end
end