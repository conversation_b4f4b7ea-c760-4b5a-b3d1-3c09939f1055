% 计算梯度
function gradient = cal_gradient_sen(b,theta_BS_t,phi_BS_t,lambda,tm_iter)
    x_angle = sin(theta_BS_t).* cos(phi_BS_t);
    y_angle = cos(theta_BS_t);
    angle_gather = [x_angle,y_angle];
    diff_dis = angle_gather * tm_iter;   
    varrho = 2*pi/lambda * diff_dis + angle(b);

    temp1 = sum(abs(b) .* sin(theta_BS_t) .* cos(phi_BS_t) .* sin(varrho));    
    del_x = -2*pi/lambda * temp1 ;   % 对x求导,式(66a)

    temp2 = sum(abs(b) .* cos(theta_BS_t) .* sin(varrho));
    del_y = -2*pi/lambda * temp2 ;   % 对y求导

    gradient = [del_x , del_y]'; %梯度结果 
end