% 根据移动后的天线位置生成新的信道矩阵 H
function [H_BS_IRS,h_K,H_1,H_2] = cal_H(T,theta_BS_t,phi_BS_t,theta_BS_CU_t,phi_BS_CU_t,F_IRS,G_IRS_CU,Sigma_Rayleigh,Sigma_Rician,Sigma_Rician_IRS ,Phi,lambda,L,M,K,N)


G_BS_IRS = cal_FRV_Matrix(T,theta_BS_t,phi_BS_t,lambda);   % BS-IRS信道的FRM
%BS直接到第k个用户的发射FRM
G_BS_CU=ones(L,M,K);

for k=1:K
    G_BS_CU(:,:,k) =  cal_FRV_Matrix(T,theta_BS_CU_t(:,k),phi_BS_CU_t(:,k),lambda);   % BS-CU信道的发射FRM   
end



% 信道矩阵
H_BS_IRS =  F_IRS' * Sigma_Rician_IRS * G_BS_IRS;     % BS to IRS

H_1=ones(N,K);% IRS-CU 的信道
H_2=ones(M,K);% BS-CU 的信道
h_K=ones(M,K);% BS-CU 的等效信道
for k=1:K
    H_2(:,k)=(ones(1,L)*Sigma_Rayleigh(:,:,k)*G_BS_CU(:,:,k))';%BS直接到第k个用户的信道
    H_1(:,k)=(ones(1,L)*Sigma_Rician(:,:,k)*G_IRS_CU(:,:,k))';%IRS到第k个用户的信道
    h_K(:,k)=(H_1(:,k)'*Phi*H_BS_IRS+H_2(:,k)')';
end
end